package unite.nz.hamiltonjet.controllers.process;

import jakarta.annotation.PostConstruct;
import jakarta.faces.application.FacesMessage;
import jakarta.faces.context.FacesContext;
import jakarta.faces.model.SelectItem;
import jakarta.faces.view.ViewScoped;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.transaction.Transactional;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.jboss.logging.Logger;
import org.primefaces.PrimeFaces;
import org.primefaces.event.RowEditEvent;
import unite.nz.hamiltonjet.controllers.security.interceptors.Authenticated;
import unite.nz.hamiltonjet.controllers.security.interceptors.Authorised;
import unite.nz.hamiltonjet.entity.enums.ApplicationRole;
import unite.nz.hamiltonjet.entity.enums.JetSizeKind;
import unite.nz.hamiltonjet.entity.enums.ProcessType;
import unite.nz.hamiltonjet.entity.process.Process;
import unite.nz.hamiltonjet.entity.process.SlotboardProcess;
import unite.nz.hamiltonjet.service.process.SlotboardProcessService;
import unite.nz.hamiltonjet.utils.SerialVersionGenerator;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Named
@ViewScoped
@Authenticated
@Authorised(ApplicationRole.USER)
public class ProcessesController implements Serializable {

    @Serial
    private static final long serialVersionUID = SerialVersionGenerator.get();

    @Inject
    Logger logger;

    @Inject
    SlotboardProcessService processService; // Use your concrete service

    @Getter @Setter
    private JetSizeKind selectedJetSizeKind = JetSizeKind.LARGE;

    @Getter @Setter
    private ProcessType selectedProcessType;

    @Getter
    private List<SelectItem> processChoices = new ArrayList<>();

    private List<SlotboardProcess> processesForSelectedType = new ArrayList<>();

    @Getter
    private List<ProcessStep> chosenProcessSteps = new ArrayList<>();

    @Getter @Setter
    private ProcessStep pendingActionStep;
    @Getter @Setter
    private String originalStepName, newStepName, dialogNewStepName, mergeTargetStepName;

    @PostConstruct
    @Transactional
    public void init() {
        logger.debug("> init ProcessesController");
        loadProcessTypeChoices();
        logger.debug("< init ProcessesController");
    }

    // Listeners for jet size, process type, and row changes changes
    public void jetSizeChangeListener() {
        logger.debugf("Jet size changed to: %s", selectedJetSizeKind);

        // Clear current selections
        selectedProcessType = null;
        processesForSelectedType = new ArrayList<>();
        chosenProcessSteps = new ArrayList<>();    // Clear chosen process steps

        // Relead process choices for the new jet size
        loadProcessTypeChoices();
    }

    public void selectedProcessTypeListener() {
        logger.debugf("Process type selection changed to: %s", selectedProcessType);
        processesForSelectedType = new ArrayList<>();
        chosenProcessSteps = new ArrayList<>();    // Clear chosen process steps when type changes

        if (selectedProcessType != null) {
            // Get all processes for the selected jet size and filter by process type
            List<SlotboardProcess> allProcesses = this.getProcessesForJetSize(selectedJetSizeKind);
            processesForSelectedType = allProcesses.stream()
                    .filter(p -> p.getProcessType().equals(selectedProcessType))
                    .sorted(Comparator.comparingInt(Process::getOrderNumber))
                    .collect(Collectors.toList()); // This creates a mutable ArrayList

            logger.debugf("Found %d process for type %s.", processesForSelectedType.size(), selectedProcessType);
        }

        // Create process steps ONCE when process type changes
        createProcessSteps();
    }

    // Creates process steps based on the selected process and store them
    private void createProcessSteps() {
        chosenProcessSteps.clear();

        for (int i = 0; i < processesForSelectedType.size(); i++) {
            SlotboardProcess process = processesForSelectedType.get(i);
            ProcessStep step = new ProcessStep();
            step.setName(process.getName());
            step.setOrderNumber(process.getOrderNumber());
            step.setStepId("step_" + process.getId() + "_" + i);    // Unique ID for each step
            step.setSlotBoardProcessId(process.getId());            // Store the ID of the SlotboardProcess, in order to persist back into a DB

            logger.infof("Creating step with ID: %s for process: %s, SlotBoardProcess ID: %d", step.getStepId(), step.getName(), step.getSlotBoardProcessId());

            // Set merge/duplicate capabilities based on position in the process
            step.setCanMergePrevious(i > 0); // Can merge if not the first step
            step.setCanDuplicate(true); // All steps can be duplicated for now
            step.setCanDelete(true); // All steps can be deleted for now

            // Set some default rates - you may want to make these configurable
            step.setMergeBelowBucketRate(50);
            step.setDuplicateAboveBucketRate(80);
            chosenProcessSteps.add(step);
        }
    }

    public void onRowEdit(RowEditEvent<SlotboardProcess> event) {
        SlotboardProcess editedProcess = event.getObject();
        logger.debugf("Row selection changed to: %s", editedProcess.getName());

        try {
            // Save the edited process to DB
            // TODO inject processService and call save method
            // processService.saveOrUpdate(editedProcess);

            // Handle the edit based on what changed
            boolean success = handleProcessEdit(editedProcess);
            if (success) {
                FacesMessage msg = new FacesMessage("Process updated",
                        "Process: " + editedProcess.getName() + " updated successfully");
                FacesContext.getCurrentInstance().addMessage(null, msg);

                // Programmatically update UI components after successful edit
                updateUIAfterRowEdit();
            } else {
                FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                        "Update failed", "Could not update process: " + editedProcess.getName());
                FacesContext.getCurrentInstance().addMessage(null, msg);
            }
        } catch (Exception e) {
            logger.errorf("Error updating process: %s", e.getMessage());
            FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_ERROR, "Update of the process failed", e.getMessage());
            FacesContext.getCurrentInstance().addMessage(null, msg);
        }
    }

    public void onRowCancel(RowEditEvent<SlotboardProcess> event) {
        FacesMessage msg = new FacesMessage("Edit Cancelled", "Edit of process: " + event.getObject().getName() + " cancelled");
        FacesContext.getCurrentInstance().addMessage(null, msg);
    }


    private boolean handleProcessEdit(SlotboardProcess editedProcess) {
        // Find the original process in our lists to detect changes
        ProcessStep originalProcess = findOriginalProcess(editedProcess.getId());
        if (originalProcess == null) {
            logger.errorf("Could not find original process step with ID: %d", editedProcess.getId());
            return false;
        }

        // Check what changed
        boolean nameChanged = !Objects.equals(originalProcess.getName(), editedProcess.getName());
        boolean orderChanged = originalProcess.getOrderNumber() != editedProcess.getOrderNumber();

        logger.debugf("Changes detected in: - Name: %s, Order: %s", nameChanged, orderChanged);

        if (orderChanged && nameChanged) {
            boolean nameChangeSucceeded = handleNameChange(editedProcess);
            boolean orderChangeSucceeded = handleOrderNumberChange(editedProcess, originalProcess);

            return nameChangeSucceeded && orderChangeSucceeded;
        }

        if (orderChanged) {
            return handleOrderNumberChange(editedProcess, originalProcess);
        } else if (nameChanged) {
            return handleNameChange(editedProcess);
        }

        // No significant changes detected
        return true;
    }

    private ProcessStep findOriginalProcess(Long processId) {
        return this.chosenProcessSteps.stream()
                .filter(p -> Objects.equals(p.getSlotBoardProcessId(), processId))
                .findFirst()
                .orElse(null);
    }

    private boolean handleOrderNumberChange(SlotboardProcess editedProcess, ProcessStep originalProcess) {
        int newOrder = editedProcess.getOrderNumber();
        int oldOrder = originalProcess.getOrderNumber();

        logger.infof("Order number change: %s from %d to %d",
                editedProcess.getName(), oldOrder, newOrder);

        // Validate new order number
        if (newOrder < 1 || newOrder > this.processesForSelectedType.size()) {
            logger.errorf("Invalid order number: %d. Must be between 1 and %d",
                    newOrder, this.processesForSelectedType.size());
            return false;
        }

        try {
            // Reorder the processes
            reorderProcesses(editedProcess, oldOrder, newOrder);

            // Update all affected processes in the database
            boolean dbSuccess = this.processService.insertOrUpdateProcesses(this.processesForSelectedType);

            if (dbSuccess) {
                // Synchronize UI state
                synchronizeUIAfterEdit();
                logger.infof("Successfully reordered processes due to order change");
                return true;
            } else {
                logger.errorf("Database update failed during order number change");
                return false;
            }

        } catch (Exception e) {
            logger.errorf(e, "Error handling order number change");
            return false;
        }
    }

    private void reorderProcesses(SlotboardProcess editedProcess, int oldOrder, int newOrder) {
        // Remove the edited process from its current position
        this.processesForSelectedType.removeIf(p -> Objects.equals(p.getId(), editedProcess.getId()));

        // Insert it at the new position (adjust for 0-based index)
        int insertIndex = Math.min(newOrder - 1, this.processesForSelectedType.size());
        this.processesForSelectedType.add(insertIndex, editedProcess);

        // Update all order numbers to be sequential
        for (int i = 0; i < this.processesForSelectedType.size(); i++) {
            this.processesForSelectedType.get(i).setOrderNumber(i + 1);
        }

        logger.debugf("Reordered %d processes", this.processesForSelectedType.size());
    }

    private boolean handleNameChange(SlotboardProcess editedProcess) {
        logger.infof("Name change for process ID %d: %s",
                editedProcess.getId(), editedProcess.getName());

        try {
            // Update in database
            boolean dbSuccess = this.processService.insertOrUpdateProcess(editedProcess);

            if (dbSuccess) {
                // Update the process in our local list
                updateProcessInLocalList(editedProcess);

                // Synchronize UI state
                synchronizeUIAfterEdit();

                logger.infof("Successfully updated process name");
                return true;
            } else {
                logger.errorf("Database update failed for name change");
                return false;
            }
        } catch (Exception e) {
            logger.errorf(e, "Error handling name change");
            return false;
        }
    }

    private void updateProcessInLocalList(SlotboardProcess updatedProcess) {
        for (int i = 0; i < this.processesForSelectedType.size(); i++) {
            SlotboardProcess p = this.processesForSelectedType.get(i);
            if (Objects.equals(p.getId(), updatedProcess.getId())) {
                this.processesForSelectedType.set(i, updatedProcess);
                break;
            }
        }
    }

    private void synchronizeUIAfterEdit() {
        logger.debugf("Synchronizing UI after edit...");

        // Sort lists to maintain consistency BEFORE recreating steps
        ensureListsAreSorted();

        // Recreate process steps based on updated process list
        createProcessSteps();

        // Ensure ProcessStep IDs are synchronized with database
        syncProcessStepIdsWithDB();

        logger.debugf("UI synchronization complete. ProcessSteps: %d, SlotboardProcesses: %d",
                this.chosenProcessSteps.size(), this.processesForSelectedType.size());
    }

    /**
     * Ensures both process lists are properly sorted by order number.
     * Called at strategic points to maintain data consistency.
     */
    private void ensureListsAreSorted() {
        if (this.processesForSelectedType != null) {
            this.processesForSelectedType.sort(Comparator.comparing(SlotboardProcess::getOrderNumber));
        }
        if (this.chosenProcessSteps != null) {
            this.chosenProcessSteps.sort(Comparator.comparing(ProcessStep::getOrderNumber));
        }
    }

    /**
     * Programmatically updates UI components after a successful row edit.
     * This ensures both table and timeline are refreshed with synchronized data.
     */
    private void updateUIAfterRowEdit() {
        logger.debugf("Updating UI components after row edit...");

        try {
            // Strategy: Update individual components in sequence to avoid JSF component tree corruption
            // First update the table data only
            PrimeFaces.current().ajax().update("processShowForm:processTableAndTimelinePanel:processTablePanel:processTable");

            // Then update the timeline
            PrimeFaces.current().ajax().update("processShowForm:processTableAndTimelinePanel:timelinePanel");

            // Finally update the heading to reflect any count changes
            PrimeFaces.current().ajax().update("processShowForm:processTableAndTimelinePanel:selectedProcessTypeInfoHeading");

            logger.debugf("Sequential UI update completed successfully");
        } catch (Exception e) {
            logger.errorf(e, "Error updating UI after row edit");
            // Fallback: try updating the entire parent container
            try {
                logger.debugf("Attempting fallback: full container update");
                PrimeFaces.current().ajax().update("processShowForm:processTableAndTimelinePanel");
                logger.debugf("Fallback UI update completed");
            } catch (Exception fallbackException) {
                logger.errorf(fallbackException, "All UI update attempts failed");
            }
        }
    }

    private void loadProcessTypeChoices() {
        processChoices.clear();
        processesForSelectedType.clear();
        chosenProcessSteps.clear();

        // Get ALL process types for the selected jet size
        List<SlotboardProcess> allProcesses = this.getProcessesForJetSize(selectedJetSizeKind);

        logger.debugf("Loading process choices for %s jets. Found %d total processes", selectedJetSizeKind, allProcesses.size());

        // Get distinct process types for the selected jet size
        List<ProcessType> distinctProcessTypes = allProcesses.stream()
            .map(SlotboardProcess::getProcessType)
            .distinct()
            .sorted()
            .collect(Collectors.toList()); // Use collect instead of toList()

        // Create select items for each distinct process type
        for (ProcessType processType : distinctProcessTypes) {
            SelectItem item = new SelectItem(processType,
                processType.name());
            processChoices.add(item);
        }
        
        logger.debugf("Loaded %d process choices for %s jets", getProcessChoices().size(), selectedJetSizeKind);
    }

    public List<SlotboardProcess> getProcessesForJetSize(JetSizeKind jetSizeKind) {
        return processService.getAllProcesses().stream()
            .filter(p -> p.getJetSizeKind().equals(jetSizeKind))
            .sorted(Comparator.comparingInt(Process::getOrderNumber))
            .collect(Collectors.toList());
    }

    // Find step by ID to ensure we're working with the right one
    private ProcessStep findStepById(String stepId) {
        return getChosenProcessSteps().stream()
            .filter(step -> step.getStepId().equals(stepId))
            .findFirst()
            .orElse(null);
    }

    // Listeners for checkboxes
    public void setMerge(ProcessStep step) {
        logger.debugf("Merge toggled for step: %s, value: %s", step.getName(), step.isDoMerge());

        // Find the actual step in our list to ensure state persistence
        ProcessStep actualStep = findStepById(step.getStepId());

        // Clear other checkboxes for this step only if this one is being selected
        if (actualStep != null) {
            logger.debug(actualStep);
            if (actualStep.isDoMerge()) {
                actualStep.setDoDuplicate(false);
                actualStep.setDoDelete(false);
                // TODO: Show a dialog to confirm the MERGE action
                logger.debugf("Expecting merge confirmation dialog for step: %s. User should confirm.", actualStep.getName());
                this.confirmMerge(actualStep);
            }
        } else {
            logger.errorf("Could not find step with ID %s during MERGE operation. ", step.getStepId());
        }

        // TODO Implement merge logic here
        // You might want to update some database state or perform other actions
    }
    public void setDuplicate(ProcessStep step) {
        logger.debugf("Duplicate toggled for step: %s, value: %s", step.getName(), step.isDoDuplicate());

        // Find the actual step in our list to ensure state persistence
        ProcessStep actualStep = findStepById(step.getStepId());

        // Clear other checkboxes for this step only if this one is being checked
        if (actualStep != null) {
            logger.debug(actualStep);
            if (actualStep.isDoDuplicate()) {
                actualStep.setDoMerge(false);
                actualStep.setDoDelete(false);
                // TODO: Show a dialog to get new names for both duplicated steps
                logger.debugf("Expecting duplicate confirmation dialog for step: %s. User should confirm.", actualStep.getName());
                this.confirmDuplicate(actualStep);
            }
        } else {
            logger.errorf("Could not find step with ID %s during DUPLICATE operation. ", step.getStepId());
        }

        // TODO Implement duplicate logic here
        // You might want to update some database state or perform other actions
    }
    public void setDelete(ProcessStep step) {
        logger.debugf("Delete toggled for step: %s, value: %s", step.getName(), step.isDoDelete());

        // Find the actual step in our list to ensure state persistence
        ProcessStep actualStep = findStepById(step.getStepId());

        // Clear other checkboxes for this step only if this one is being checked
        if (actualStep != null) {
            logger.debug(actualStep);
            if (actualStep.isDoDelete()) {
                logger.debugf("Delete action selected for step: %s", step.getName());
                actualStep.setDoMerge(false);
                actualStep.setDoDuplicate(false);
                // Trigger the confirmation dialog
                logger.debugf("Expecting delete confirmation dialog for step: %s. User should confirm.", actualStep.getName());
                this.confirmDelete(actualStep);
            }
        } else {
            logger.errorf("Could not find step with ID %s during DELETE operation. ", step.getStepId());
        }

        // TODO Implement deletion logic here
        // You might want to update some database state or perform other actions
    }

    // Confirmation methods for each action to handle confirmation dialogs
    public void confirmMerge(@NotNull ProcessStep step) {
        ProcessStep actualStep = findStepById(step.getStepId());
        if (actualStep != null && actualStep.isDoMerge()) {
            logger.debugf("Merge action confirmed for step: %s", actualStep.getName());
            // Find the previous steps for merging
            int currentIndex = chosenProcessSteps.indexOf(actualStep);
            if (currentIndex > 0) {
                ProcessStep previousStep = chosenProcessSteps.get(currentIndex - 1);
                this.setMergeTargetStepName(previousStep.getName());

                // Store the step for the actual merge
                this.setPendingActionStep(actualStep);
                this.setNewStepName(this.getMergeTargetStepName() + " & " + this.getPendingActionStep().getName());
                this.setDialogNewStepName(this.getNewStepName());

                // Show confirmation dialog
                PrimeFaces.current().ajax().update("processShowForm:confirmationDialogsGroup:mergeConfirmDialog");
                PrimeFaces.current().executeScript("PF('mergeConfirmDialog').show();");
            }
        } else {
            logger.errorf("Could not find step with ID %s during MERGE confirmation. ", step.getStepId());
        }

        // TODO: Implement merge confirmation logic
        // This could involve showing a dialog to the user to confirm the merge action
        // and then performing the merge if the user confirms
    }
    public void confirmDuplicate(@NotNull ProcessStep step) {
        // TODO: Implement duplicate confirmation logic
        // This could involve showing a dialog to the user to confirm the duplicate action
        // and then performing the duplicate if the user confirms
        ProcessStep actualStep = findStepById(step.getStepId());
        if (actualStep != null && actualStep.isDoDuplicate()) {
            logger.debugf("Duplicate action confirmed for step: %s", actualStep.getName());
            // Set default names for the dialog
            this.setOriginalStepName(actualStep.getName());
            this.setNewStepName(actualStep.getName() + " (Copy)");

            // Store the step for the actual duplication
            this.setPendingActionStep(actualStep);

            // Show confirmation dialog
            PrimeFaces.current().ajax().update("processShowForm:confirmationDialogsGroup:duplicateConfirmDialog");
            PrimeFaces.current().executeScript("PF('duplicateConfirmDialog').show();");
        } else {
            logger.errorf("Could not find step with ID %s during DUPLICATE confirmation. ", step.getStepId());
        }
    }
    public void confirmDelete(@NotNull ProcessStep step) {
        ProcessStep actualStep = findStepById(step.getStepId());
        if (actualStep != null && actualStep.isDoDelete()) {
            logger.debugf("Delete action confirmed for step: %d - %s with ID: %s", actualStep.getOrderNumber(), actualStep.getName(), actualStep.getStepId());
            // Store the step for the actual deletion
            this.setPendingActionStep(actualStep);

            // Show confirmation dialog
            PrimeFaces.current().ajax().update("processShowForm:confirmationDialogsGroup:deleteConfirmDialog");
            PrimeFaces.current().executeScript("PF('deleteConfirmDialog').show();");
        } else {
            logger.errorf("Could not find step with ID %s during DELETE confirmation. ", step.getStepId());
        }
    }

    // Actual BL methods for each action (merge, duplicate, delete) called after confirmation
    public void executeMerge() {
        logger.debugf("Merge action executed for step: %s", this.getPendingActionStep().getName());
        if (pendingActionStep != null && this.getMergeTargetStepName() != null && !this.getMergeTargetStepName().trim().isEmpty()) {
            logger.debugf("Execution MERGE for step: %s with target: %s", pendingActionStep.getName(), this.getMergeTargetStepName());
            logger.debugf("New (merged) step name: %s", this.getDialogNewStepName());

            // TODO: Implement merge logic here
            // 1. Combining the steps
            // 2. Updating the order numbers of the subsequent steps
            // 3. Updating the database

            FacesMessage fMsg = null;
            int currentIndex = this.chosenProcessSteps.indexOf(pendingActionStep);
            if (currentIndex > 0 ) {
                ProcessStep previousStep = this.chosenProcessSteps.get(currentIndex - 1);

                // Merge names
                previousStep.setName(this.getDialogNewStepName() != null ? this.getDialogNewStepName().trim() : "N/A");

                // Copy rates from the merged step
                previousStep.setMergeBelowBucketRate(pendingActionStep.getMergeBelowBucketRate());
                previousStep.setDuplicateAboveBucketRate(pendingActionStep.getDuplicateAboveBucketRate());

                // Setting the correct capabilities from the merged step
                previousStep.setCanMergePrevious(pendingActionStep.isCanMergePrevious());
                previousStep.setCanDuplicate(pendingActionStep.isCanDuplicate());
                previousStep.setCanDelete(pendingActionStep.isCanDelete());

                // Remove the current step from lists & update DB
                Result result = removeAndUpdateListsAndDB(pendingActionStep);
//                this.chosenProcessSteps.remove(pendingActionStep);
//                updateOrderNumbersAndListOfSlotboardProcesses();

                fMsg = prepareFacesMessage(result);
            }

//            String sMsg = String.format("Process step: %s has been successfully merged into: %s.", pendingActionStep.getName(), this.getMergeTargetStepName());
//            FacesMessage fMsg = new FacesMessage(FacesMessage.SEVERITY_INFO, "Step merged", sMsg);

            // Clear the pending action step
            this.setPendingActionStep(null);
            this.setMergeTargetStepName(null);
            this.setNewStepName(null);
            this.setDialogNewStepName(null);
            this.setOriginalStepName(null);
            processService.loadProcesses();

            // Show success message
            FacesContext.getCurrentInstance().addMessage(null, fMsg);

            // Update the UI
//            PrimeFaces.current().ajax().update("processShowForm:timelinePanel");
//            PrimeFaces.current().ajax().update("processShowForm:processShowCard");
        }
    }
    public void executeDuplicate() {
        logger.debugf("Duplicate action executed for step: %s", this.getPendingActionStep().getName());
        // TODO: Implement duplicate logic here
        // to update some database state or perform other actions
        if (this.pendingActionStep != null && this.pendingActionStep.getName() != null && !this.pendingActionStep.getName().trim().isEmpty() && this.newStepName != null && !this.newStepName.trim().isEmpty()) {
            logger.debugf("Execution DUPLICATE for step: %s with new name: %s", this.pendingActionStep.getName(), this.getNewStepName());

            // TODO: Implement actual duplication logic
            // For now, just duplicate the step
            // 1. Create a new step with the new name
            // 2. Insert it into the list after the current step
            // 3. Update the order numbers of the subsequent steps
            // 4. Update the database

            // Create duplicate step
            ProcessStep duplicateStep = new ProcessStep();
            String stepId;
            if (this.pendingActionStep.getStepId().contains("-duplicate-")) {
                stepId = this.pendingActionStep.getStepId().split("-duplicate-")[0] + "-duplicate-" + (int) (System.currentTimeMillis() % Integer.MAX_VALUE);;
            } else {
                stepId = this.pendingActionStep.getStepId() + "-duplicate-" + (int) (System.currentTimeMillis() % Integer.MAX_VALUE);;
            }

            duplicateStep.setStepId(stepId);
            duplicateStep.setName(this.getNewStepName().trim());
            duplicateStep.setCanMergePrevious(true);
            duplicateStep.setCanDuplicate(true);
            duplicateStep.setCanDelete(true);
            duplicateStep.setMergeBelowBucketRate(this.pendingActionStep.getMergeBelowBucketRate());
            duplicateStep.setDuplicateAboveBucketRate(this.pendingActionStep.getDuplicateAboveBucketRate());

            // Update the original step name
            this.pendingActionStep.setName(this.getOriginalStepName().trim());

            // Insert the duplicate step after the current step
            int currentIndex = this.chosenProcessSteps.indexOf(this.pendingActionStep);
            this.chosenProcessSteps.add(currentIndex + 1, duplicateStep);
            updateOrderNumbersAndListOfSlotboardProcesses(this.chosenProcessSteps);

            // Persist the changes into DB
            //processService.merge(this.pendingActionStep);
            //processService.merge(duplicateStep);
//            processService.insertOrUpdateProcess(this.pendingActionStep);
            boolean persistResult = processService.insertOrUpdateProcesses(this.processesForSelectedType);
            String sMsg = null;
            FacesMessage fMsg = null;

            if (persistResult) {
                syncProcessStepIdsWithDB();
                // Force a refresh of the service state to ensure proper entity management
                processService.loadProcesses();
                sMsg = String.format("Process step: %s has been successfully duplicated as: %s.", this.pendingActionStep.getName(), duplicateStep.getName());
                fMsg = new FacesMessage(FacesMessage.SEVERITY_INFO, "Step duplicated succeeded", sMsg);
            } else {
                sMsg = String.format("Process step: %s failed to duplicate as: %s!", this.pendingActionStep.getName(), duplicateStep.getName());
                fMsg = new FacesMessage(FacesMessage.SEVERITY_ERROR, "Step duplication failed", sMsg);
            }

            // Clear the pending action step
            this.pendingActionStep.setDoDuplicate(false);
            this.setPendingActionStep(null);
            this.setOriginalStepName(null);
            this.setMergeTargetStepName(null);
            this.setNewStepName(null);

            // Show success message
            FacesContext.getCurrentInstance().addMessage(null, fMsg);

            // Update the UI
//            PrimeFaces.current().ajax().update("processShowForm:timelinePanel");
//            PrimeFaces.current().ajax().update("processShowForm:processTablePanel");
        }
    }
    public void executeDelete() {
        logger.debugf("Delete action executed for step: %s", this.getPendingActionStep().getName());

        // TODO: Implement delete logic here
        if (this.pendingActionStep != null) {
            logger.debugf("Execution DELETE for step: %s", this.pendingActionStep.getName());

            // Remove from the current lists & update DB
            Result result = removeAndUpdateListsAndDB(this.pendingActionStep);
            processService.loadProcesses();

            // Notify user about the result of the operation
            FacesMessage fMsg = prepareFacesMessage(result);

            // Clear the pending action step
            this.setPendingActionStep(null);

            // Show success message
            FacesContext.getCurrentInstance().addMessage(null, fMsg);

            // Update the UI - no need to call it. // TODO delete this
//            PrimeFaces.current().ajax().update("processShowForm:timelinePanel");
//            PrimeFaces.current().ajax().update("processShowForm:processTableAndTimelinePanel");
        }
    }

    private FacesMessage prepareFacesMessage(Result result) {
        String sMsg;
        FacesMessage fMsg;
        FacesMessage.Severity severity = FacesMessage.SEVERITY_INFO;
        if (result.isDeleted() && result.isListUpdated()) {
            logger.infof("Process step: %s has been successfully deleted, updated or duplicated.", this.pendingActionStep.getName());
            sMsg = String.format("Process step: %s has been successfully deleted, updated or duplicated.", this.pendingActionStep.getName());
        } else {
            logger.errorf("Failed to update, delete or duplicate process step: %s", this.pendingActionStep.getName());
            sMsg = String.format("Process step: %s failed to update, delete or duplicate!", this.pendingActionStep.getName());
            severity = FacesMessage.SEVERITY_ERROR;
        }
        fMsg = new FacesMessage(severity, "Step deleted", sMsg);
        return fMsg;
    }

    private Result removeAndUpdateListsAndDB(ProcessStep pendingActionStep) {
        logger.debugf("'chosenProcessSteps' size before removal: %d", this.chosenProcessSteps.size());
        this.chosenProcessSteps.remove(pendingActionStep);
        logger.debugf("'chosenProcessSteps' size after removal: %d", this.chosenProcessSteps.size());

        Long sbpId = pendingActionStep.getSlotBoardProcessId();
        boolean isDeleted = true;
        boolean isListUpdated = true;

        if (sbpId != null) {
            // First delete from DB
            isDeleted = this.processService.deleteProcessById(sbpId);
            logger.debugf("DB deletion result for process ID %s: %s", sbpId.toString(), isDeleted);

            // Then remove from in-memory list
            boolean removed = this.processesForSelectedType.removeIf(p -> p.getId() == sbpId);
            logger.debugf("In-memory removal result for process ID %s: %s", sbpId.toString(), removed);

            // Update remaining processes only of deletion was successful
            if (isDeleted) {
                updateOrderNumbersAndListOfSlotboardProcesses(this.chosenProcessSteps);
                isListUpdated = this.processService.insertOrUpdateProcesses(this.processesForSelectedType);

                if (isListUpdated) {
                    logger.debugf("Successfully updated list of processes for process ID %s", sbpId.toString());
                    syncProcessStepIdsWithDB();
                }
            } else {
                logger.errorf("Skipping list update due to failed DB deletion process ID %d from DB", sbpId);
                isListUpdated = false;
            }
            if (!removed) {
                logger.errorf("Failed to remove process with ID %d from processesForSelectedType", sbpId);
            }

        } else {
            // For steps that were created but not persisted, just update order numbers
            logger.warnf("ProcessStep '%s' has null slotBoardId - treating as non-persisted step", pendingActionStep.getName());
            updateOrderNumbersAndListOfSlotboardProcesses(this.chosenProcessSteps);
            isListUpdated = this.processService.insertOrUpdateProcesses(this.processesForSelectedType);

            if (isListUpdated) {
                logger.debugf("Successfully updated list of processes for non-persisted step '%s'", pendingActionStep.getName());
                syncProcessStepIdsWithDB();
            }
        }
        return new Result(isDeleted, isListUpdated);
    }

    private void syncProcessStepIdsWithDB() {
        logger.debugf("Syncing ProcessStep IDs with database...");

        // Create a map of ProcessSteps by slotboardProcessId for easier lookup
        Map<Long, ProcessStep> stepMap = this.chosenProcessSteps.stream()
                .filter(step -> step.getSlotBoardProcessId() != null)
                .collect(Collectors.toMap(ProcessStep::getSlotBoardProcessId, step -> step));

        // Update ProcessSteps based on current SlotboardProcess list
        for (SlotboardProcess sbp : this.processesForSelectedType) {
            ProcessStep correspondingStep = stepMap.get(sbp.getId());
            if (correspondingStep != null) {
                // Update the step with current process data
                correspondingStep.setName(sbp.getName());
                correspondingStep.setOrderNumber(sbp.getOrderNumber());

                logger.tracef("Synced ProcessStep '%s' (ID: %d) with order %d",
                        sbp.getName(), sbp.getId(), sbp.getOrderNumber());
            }
        }

        // Recreate the chosenProcessSteps list in the correct order
        this.chosenProcessSteps.clear();
        this.processesForSelectedType.stream()
                .sorted(Comparator.comparing(SlotboardProcess::getOrderNumber))
                .forEach(sbp -> {
                    ProcessStep step = stepMap.get(sbp.getId());
                    if (step != null) {
                        this.chosenProcessSteps.add(step);
                    } else {
                        // Create new ProcessStep if not found (shouldn't happen in normal flow)
                        ProcessStep newStep = createProcessStepFromSlotboardProcess(sbp);
                        this.chosenProcessSteps.add(newStep);
                        logger.warnf("Created new ProcessStep for existing SlotboardProcess: %s", sbp.getName());
                    }
                });

        logger.debugf("Completed ProcessStep synchronization. Total steps: %d", this.chosenProcessSteps.size());
    }

    private ProcessStep createProcessStepFromSlotboardProcess(SlotboardProcess sbp) {
        ProcessStep step = new ProcessStep();
        step.setSlotBoardProcessId(sbp.getId());
        step.setName(sbp.getName());
        step.setOrderNumber(sbp.getOrderNumber());
        step.setStepId("step_" + sbp.getId() + "_" + sbp.getOrderNumber());
        // Set other default values as needed
        step.setCanDelete(true);
        step.setCanDuplicate(true);
        step.setCanMergePrevious(sbp.getOrderNumber() > 1);
        return step;
    }

    private record Result(boolean isDeleted, boolean isListUpdated) {
    }

    // Cancel methods for each action to handle canceling the confirmation dialogs
    public void cancelMerge() {
        logger.debugf("Merge action cancelled for step: %s", this.getPendingActionStep().getName());
        // Clear the pending action step
        if (pendingActionStep != null) {
            pendingActionStep.setDoMerge(false);
        }
        this.setPendingActionStep(null);
        this.setMergeTargetStepName(null);
        this.setNewStepName(null);
        this.setDialogNewStepName(null);
    }
    public void cancelDuplicate() {
        logger.debugf("Duplicate action cancelled for step: %s", this.getPendingActionStep().getName());
        // Clear the pending action step
        if (pendingActionStep != null) {
            pendingActionStep.setDoDuplicate(false);
        }
        this.setPendingActionStep(null);
        this.setOriginalStepName(null);
        this.setNewStepName(null);
    }
    public void cancelDelete() {
        logger.debugf("Delete action cancelled for step: %s", this.getPendingActionStep().getName());
        // Clear the pending action step
        if (pendingActionStep != null) {
            pendingActionStep.setDoDelete(false);
        }
        this.setPendingActionStep(null);
    }

    // Helper method to update order numbers after modification and insert new steps into the list of SlotboardProcess
    private void updateOrderNumbersAndListOfSlotboardProcesses(List<ProcessStep> chosenProcessSteps) {
        logger.debugf("updateOrderNumbersAndListOfSlotboardProcesses() called with %d steps", chosenProcessSteps.size());
        this.processesForSelectedType.clear();

        // Update the capabilities of the first and last steps (if it is the only step)
        if (!chosenProcessSteps.isEmpty()) {
            chosenProcessSteps.getFirst().setCanMergePrevious(false);
            if (chosenProcessSteps.size() == 1) chosenProcessSteps.getLast().setCanDelete(false);
        }
        for (int i = 0; i < chosenProcessSteps.size(); i++) {
            ProcessStep step = chosenProcessSteps.get(i);
            step.setOrderNumber(i + 1);
            logger.debugf("Updated ProcessStep's and corresponding SlotboardProcess's    order number %d - %s, %s, sbpId: %d", step.getOrderNumber(), step.getName(), step.getStepId(), step.getSlotBoardProcessId());

            // Create or Update the corresponding SlotboardProcess if present
            SlotboardProcess sbp;
            if (step.getSlotBoardProcessId() != null) {
                // Existing process = find it or create a new one wiht the ID
                sbp = this.processesForSelectedType.stream()
                        .filter(p -> p.getId() == step.getSlotBoardProcessId())
                        .findFirst()
                        .orElse(new SlotboardProcess());

                if (sbp.getId() == 0) {
                    sbp.setId(step.getSlotBoardProcessId());
                }
            } else {
                // New process
                sbp = new SlotboardProcess();
            }

            sbp.setName(step.getName());
            sbp.setOrderNumber(step.getOrderNumber());
            sbp.setProcessType(this.selectedProcessType);
            sbp.setJetSizeKind(this.selectedJetSizeKind);

            this.processesForSelectedType.add(sbp);
            this.processesForSelectedType.sort(Comparator.comparingInt(Process::getOrderNumber));

            logger.debugf("Updated SlotboardProcess's order number %d - %s, sbpId: %d", sbp.getOrderNumber(), sbp.getName(), sbp.getId(), sbp.getClass());      // Last param is to workaround a bug in the JBoss Logger

//            SlotboardProcess process = processService.getProcessById(chosenProcessSteps.get(i).getSlotBoardProcessId());
//            if (process != null) {
//                process.setOrderNumber(chosenProcessSteps.get(i).getOrderNumber());
//                processService.merge(process);
//            }
        }
    }

    public void forceUpdate() {
        logger.info("> Force DB reload for Processes.");

        // Reset all controller state
        selectedProcessType = null;
        selectedJetSizeKind = JetSizeKind.LARGE;  // Reset to default
        processesForSelectedType = new ArrayList<>();
        processChoices = new ArrayList<>();
        chosenProcessSteps = new ArrayList<>();

        // Force reload from DB
        processService.loadProcesses();

        // Reload process choices for the default jet size
        loadProcessTypeChoices();

        // Force UI refresh by requesting an update of key components
        PrimeFaces.current().ajax().update("processShowForm:processShowCard");

        logger.info("< Force DB reload for Processes.");
    }

    // Getter for displaying purposes - shows the selected procss type info
    public String getSelectedProcessTypeInfo() {
        if (this.selectedProcessType != null && !this.processesForSelectedType.isEmpty()) {
            String selectedProcessTypeInfo = String.format("Process Type: %s (%d process steps)", this.selectedProcessType, this.processesForSelectedType.size());
            logger.debugf("Selected process type info: \n%s", selectedProcessTypeInfo);
            return selectedProcessTypeInfo;
        }
        return null;
    }
    // Getter used by front-end - returns the list without additional sorting to avoid interference during updates
    public List<SlotboardProcess> getProcessesForSelectedType() {
        return this.processesForSelectedType;
    }

    // Inner class to represent a process step for the timeline
    @Getter @Setter @ToString
    public static class ProcessStep implements Serializable {
        private String stepId; // Unique identifier for this step
        private Long slotBoardProcessId;
        private String name;
        private int orderNumber;
        private boolean canMergePrevious;
        private boolean canDuplicate;
        private boolean canDelete;
        private int mergeBelowBucketRate;
        private int duplicateAboveBucketRate;

        // Individual checkbox states for each step
        private boolean doMerge = false;
        private boolean doDuplicate = false;
        private boolean doDelete = false;

    }
}